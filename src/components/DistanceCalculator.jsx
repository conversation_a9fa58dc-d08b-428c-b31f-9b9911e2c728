// Distance calculation utilities for room code positioning

/**
 * Calculate the centroid (center point) of a bounding box annotation
 * @param {Object} annotation - The annotation object (rectangle or polygon)
 * @returns {Object} - {x, y} coordinates of the centroid
 */
export const calculateAnnotationCentroid = (annotation) => {
  if (!annotation) return { x: 0, y: 0 }

  if (annotation.type === 'rectangle') {
    return {
      x: annotation.x + annotation.width / 2,
      y: annotation.y + annotation.height / 2
    }
  } else if (annotation.type === 'polygon' && annotation.points && annotation.points.length > 0) {
    // Calculate centroid of polygon using average of all points
    const sumX = annotation.points.reduce((sum, point) => sum + point.x, 0)
    const sumY = annotation.points.reduce((sum, point) => sum + point.y, 0)
    return {
      x: sumX / annotation.points.length,
      y: sumY / annotation.points.length
    }
  }

  return { x: 0, y: 0 }
}

/**
 * Calculate Euclidean distance between two points
 * @param {Object} point1 - {x, y} coordinates
 * @param {Object} point2 - {x, y} coordinates
 * @returns {number} - Distance between the points
 */
export const calculateDistance = (point1, point2) => {
  if (!point1 || !point2) return Infinity
  
  const dx = point1.x - point2.x
  const dy = point1.y - point2.y
  return Math.sqrt(dx * dx + dy * dy)
}

/**
 * Sort room names by distance from annotation centroid using cached room code coordinates
 * @param {Array} roomNames - Array of room name strings
 * @param {Object} annotation - The annotation object to calculate distance from
 * @param {Map} roomCodeCache - Map of room codes to their coordinate data
 * @param {Function} extractRoomCode - Function to extract room code from room name
 * @returns {Array} - Array of objects with {roomName, distance, hasCoordinates, coordinates}
 */
export const sortRoomNamesByDistance = (roomNames, annotation, roomCodeCache, extractRoomCode) => {
  if (!roomNames || !annotation || !roomCodeCache || !extractRoomCode) {
    // Return original room names with no distance info if required data is missing
    return roomNames.map(roomName => ({
      roomName,
      distance: Infinity,
      hasCoordinates: false,
      coordinates: null
    }))
  }

  const annotationCentroid = calculateAnnotationCentroid(annotation)
  
  const roomsWithDistance = roomNames.map(roomName => {
    // Extract room code from room name
    const roomCode = extractRoomCode(roomName)
    
    if (roomCode && roomCodeCache.has(roomCode)) {
      // Get cached coordinates for this room code
      const codeData = roomCodeCache.get(roomCode)

      // If there are multiple matches, find the closest one to the annotation
      let closestMatch = null
      let minDistance = Infinity

      if (codeData.allMatches && codeData.allMatches.length > 1) {
        // Multiple matches available - find the closest one
        codeData.allMatches.forEach(match => {
          const distance = calculateDistance(annotationCentroid, match.coordinates)
          if (distance < minDistance) {
            minDistance = distance
            closestMatch = match
          }
        })

        console.log(`Room code "${roomCode}" has ${codeData.allMatches.length} matches. Closest match is at distance ${Math.round(minDistance)}px`)
      } else {
        // Single match or fallback to primary coordinates
        const roomCoordinates = codeData.coordinates
        minDistance = calculateDistance(annotationCentroid, roomCoordinates)
        closestMatch = {
          coordinates: roomCoordinates,
          pdfCoordinates: codeData.pdfCoordinates,
          textItem: codeData.textItem,
          pdfIndex: codeData.pdfIndex,
          pageIndex: codeData.pageIndex
        }
      }

      return {
        roomName,
        distance: minDistance,
        hasCoordinates: true,
        coordinates: closestMatch.coordinates,
        roomCode,
        codeData,
        closestMatch, // Include the closest match details
        totalMatches: codeData.allMatches ? codeData.allMatches.length : 1
      }
    } else {
      // No coordinates available for this room
      return {
        roomName,
        distance: Infinity,
        hasCoordinates: false,
        coordinates: null,
        roomCode
      }
    }
  })

  // Sort by distance (closest first), then by room name alphabetically for ties
  return roomsWithDistance.sort((a, b) => {
    // Rooms with coordinates come first
    if (a.hasCoordinates && !b.hasCoordinates) return -1
    if (!a.hasCoordinates && b.hasCoordinates) return 1
    
    // If both have coordinates, sort by distance
    if (a.hasCoordinates && b.hasCoordinates) {
      if (a.distance !== b.distance) {
        return a.distance - b.distance
      }
    }
    
    // For ties or rooms without coordinates, sort alphabetically
    return a.roomName.localeCompare(b.roomName)
  })
}

/**
 * Format distance for display
 * @param {number} distance - Distance in pixels
 * @returns {string} - Formatted distance string
 */
export const formatDistance = (distance) => {
  if (distance === Infinity || isNaN(distance)) {
    return 'No location'
  }
  
  if (distance < 1) {
    return '< 1px'
  } else if (distance < 100) {
    return `${Math.round(distance)}px`
  } else {
    return `${Math.round(distance / 10) * 10}px`
  }
}

/**
 * Get the closest room names within a certain distance threshold
 * @param {Array} sortedRooms - Array from sortRoomNamesByDistance
 * @param {number} maxDistance - Maximum distance threshold (default: 200px)
 * @returns {Array} - Array of room names within the threshold
 */
export const getCloseRooms = (sortedRooms, maxDistance = 200) => {
  return sortedRooms.filter(room => 
    room.hasCoordinates && room.distance <= maxDistance
  )
}

/**
 * Create a visual indicator for distance in the dropdown
 * @param {number} distance - Distance in pixels
 * @param {boolean} hasCoordinates - Whether the room has coordinates
 * @returns {string} - Visual indicator (emoji or symbol)
 */
export const getDistanceIndicator = (distance, hasCoordinates) => {
  if (!hasCoordinates) return '📍' // Pin for rooms without coordinates
  
  if (distance < 50) return '🎯' // Bullseye for very close
  if (distance < 100) return '🔴' // Red circle for close
  if (distance < 200) return '🟡' // Yellow circle for medium
  if (distance < 500) return '🟠' // Orange circle for far
  return '⚪' // White circle for very far
}

/**
 * Hook for using distance-based room sorting
 * @param {Map} roomCodeCache - Room code cache from useRoomCodeSearcher
 * @param {Function} extractRoomCode - Room code extraction function
 * @returns {Object} - Object with sorting and utility functions
 */
export const useDistanceCalculator = (roomCodeCache, extractRoomCode) => {
  const sortRoomsByDistance = (roomNames, annotation) => {
    return sortRoomNamesByDistance(roomNames, annotation, roomCodeCache, extractRoomCode)
  }

  const getAnnotationCentroid = (annotation) => {
    return calculateAnnotationCentroid(annotation)
  }

  const getDistance = (point1, point2) => {
    return calculateDistance(point1, point2)
  }

  return {
    sortRoomsByDistance,
    getAnnotationCentroid,
    getDistance,
    formatDistance,
    getDistanceIndicator,
    getCloseRooms
  }
}
